@extends('layouts.admin')

@section('title', 'User Dashboard - Field Management System')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">User Dashboard</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Welcome Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-lg bg-primary-transparent">
                                <i class="ti ti-user-check fs-18"></i>
                            </span>
                        </div>
                        <div>
                            <h3 class="fw-semibold mb-1">Welcome back, {{ auth()->user()->name ?? 'Guest' }}!</h3>
                            <p class="text-muted mb-0">You have client access to the Field Management System. Manage your
                                profile and account settings below.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Quick Actions</div>
                </div>
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('profile.show') }}" class="btn btn-outline-primary w-100">
                                <i class="ti ti-user me-2"></i>View Profile
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('profile.edit') }}" class="btn btn-outline-success w-100">
                                <i class="ti ti-settings me-2"></i>Edit Settings
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('profile.edit') }}" class="btn btn-outline-info w-100">
                                <i class="ti ti-lock me-2"></i>Security
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="javascript:void(0);" class="btn btn-outline-warning w-100" onclick="contactSupport()">
                                <i class="ti ti-help me-2"></i>Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="row">
        <!-- Profile Management Card -->
        <div class="col-xl-6 col-lg-6 col-md-6">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-primary">
                            <i class="ti ti-user fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">Profile Management</h5>
                    </div>
                    <p class="text-muted mb-3">View and manage your profile information, personal details, and contact
                        information.</p>
                    <div class="d-flex gap-2">
                        <a href="{{ route('profile.show') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-eye me-1"></i>View
                        </a>
                        <a href="{{ route('profile.edit') }}" class="btn btn-outline-primary btn-sm">
                            <i class="ti ti-edit me-1"></i>Edit
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Security Card -->
        <div class="col-xl-6 col-lg-6 col-md-6">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-success">
                            <i class="ti ti-shield-check fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">Account Security</h5>
                    </div>
                    <p class="text-muted mb-3">Update your password, enable two-factor authentication, and manage security
                        preferences.</p>
                    <div class="d-flex gap-2">
                        <a href="{{ route('profile.edit') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-settings me-1"></i>Settings
                        </a>
                        <a href="{{ route('profile.edit') }}" class="btn btn-outline-success btn-sm">
                            <i class="ti ti-lock me-1"></i>Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Account Information -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Your Account Information</div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Role</label>
                                <div class="fw-semibold">
                                    {{ auth()->user() ? ucfirst(str_replace('_', ' ', auth()->user()->role)) : 'Client' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <div class="fw-semibold">{{ auth()->user()->email ?? 'N/A' }}</div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Username</label>
                                <div class="fw-semibold">{{ auth()->user()->username ?? 'Not set' }}</div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Member Since</label>
                                <div class="fw-semibold">{{ auth()->user()->created_at?->format('M d, Y') ?? 'N/A' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Access Notice -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="ti ti-info-circle fs-20 text-info"></i>
                            </div>
                            <div>
                                <h6 class="fw-semibold mb-1">User Access Level</h6>
                                <p class="mb-0">As a user, you have access to your personal profile and account
                                    settings. For additional features or assistance, please contact your administrator using
                                    the support button above.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function contactSupport() {
            // You can customize this function to open a support modal, redirect to a contact form, or show contact information
            alert(
                'Support Contact:\n\nEmail: <EMAIL>\nPhone: (*************\n\nOr contact your system administrator for assistance.'
            );
        }
    </script>
@endsection
